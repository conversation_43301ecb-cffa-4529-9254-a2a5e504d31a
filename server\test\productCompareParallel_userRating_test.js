/**
 * ProductService V4 并行版本用户评分功能测试脚本
 * 测试新增的用户评分查询功能
 * 功能: 测试在产品对比API中传入用户ID，查询用户评分
 * 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro
 */

const mongoose = require('mongoose');
const { compareProductsByNamesV4Parallel } = require('../src/services/product/productCompareParallel');
const User = require('../src/models/User');
const ComparisonRating = require('../src/models/ComparisonRating');
const ProductComparisonV4Cache = require('../src/models/ProductComparisonV4Cache');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}

/**
 * 创建测试用户
 */
async function createTestUser() {
  try {
    // 先删除可能存在的测试用户
    await User.deleteOne({ nickname: 'TestUser_Rating' });
    
    const testUser = new User({
      nickname: 'TestUser_Rating',
      phone: '13800138000',
      avatar: '/assets/images/test-avatar.png',
      gender: 'secret',
      age: 25
    });
    
    await testUser.save();
    console.log('✅ 测试用户创建成功，ID:', testUser._id);
    return testUser._id;
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error.message);
    throw error;
  }
}

/**
 * 创建测试评分
 */
async function createTestRating(comparisonCacheId, userId, rating = 4.5) {
  try {
    // 先删除可能存在的评分
    await ComparisonRating.deleteOne({ 
      comparisonCacheId: comparisonCacheId, 
      userId: userId 
    });
    
    const testRating = new ComparisonRating({
      comparisonCacheId: comparisonCacheId,
      userId: userId,
      rating: rating,
      productNames: ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'],
      productCategory: '手机'
    });
    
    await testRating.save();
    console.log('✅ 测试评分创建成功，评分:', rating);
    return testRating;
  } catch (error) {
    console.error('❌ 创建测试评分失败:', error.message);
    throw error;
  }
}

/**
 * 测试场景1: 不传用户ID
 */
async function testWithoutUserId() {
  console.log('\n📋 测试场景1: 不传用户ID');
  console.log('-'.repeat(50));
  
  const productNames = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];
  
  try {
    const result = await compareProductsByNamesV4Parallel(productNames);
    
    if (result.success) {
      console.log('✅ 对比成功');
      console.log('📊 返回结果包含userRating字段:', 'userRating' in result.data);
      
      if ('userRating' in result.data) {
        console.log('⚠️ 意外：不传用户ID时不应该包含userRating字段');
      } else {
        console.log('✅ 正确：不传用户ID时没有userRating字段');
      }
      
      return result.data.comparisonCacheId;
    } else {
      console.log('❌ 对比失败:', result.error);
      return null;
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return null;
  }
}

/**
 * 测试场景2: 传入用户ID但用户未评分
 */
async function testWithUserIdNoRating(userId) {
  console.log('\n📋 测试场景2: 传入用户ID但用户未评分');
  console.log('-'.repeat(50));
  
  const productNames = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];
  
  try {
    const result = await compareProductsByNamesV4Parallel(productNames, userId.toString());
    
    if (result.success) {
      console.log('✅ 对比成功');
      console.log('📊 返回结果包含userRating字段:', 'userRating' in result.data);
      
      if ('userRating' in result.data) {
        const userRating = result.data.userRating;
        console.log('📝 用户评分信息:', userRating);
        
        if (userRating.hasRated === false && userRating.rating === null) {
          console.log('✅ 正确：用户未评分，hasRated为false，rating为null');
        } else {
          console.log('⚠️ 意外：用户评分状态不正确');
        }
      } else {
        console.log('⚠️ 意外：传入用户ID时应该包含userRating字段');
      }
      
      return result.data.comparisonCacheId;
    } else {
      console.log('❌ 对比失败:', result.error);
      return null;
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return null;
  }
}

/**
 * 测试场景3: 传入用户ID且用户已评分
 */
async function testWithUserIdHasRating(userId, comparisonCacheId) {
  console.log('\n📋 测试场景3: 传入用户ID且用户已评分');
  console.log('-'.repeat(50));
  
  if (!comparisonCacheId) {
    console.log('⚠️ 跳过测试：没有有效的comparisonCacheId');
    return;
  }
  
  try {
    // 先创建测试评分
    await createTestRating(comparisonCacheId, userId, 4.5);
    
    const productNames = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];
    const result = await compareProductsByNamesV4Parallel(productNames, userId.toString());
    
    if (result.success) {
      console.log('✅ 对比成功');
      console.log('📊 返回结果包含userRating字段:', 'userRating' in result.data);
      
      if ('userRating' in result.data) {
        const userRating = result.data.userRating;
        console.log('📝 用户评分信息:', userRating);
        
        if (userRating.hasRated === true && userRating.rating === 4.5) {
          console.log('✅ 正确：用户已评分，hasRated为true，rating为4.5');
        } else {
          console.log('⚠️ 意外：用户评分状态不正确');
        }
      } else {
        console.log('⚠️ 意外：传入用户ID时应该包含userRating字段');
      }
    } else {
      console.log('❌ 对比失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

/**
 * 测试场景4: 传入无效的用户ID
 */
async function testWithInvalidUserId() {
  console.log('\n📋 测试场景4: 传入无效的用户ID');
  console.log('-'.repeat(50));
  
  const productNames = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];
  const invalidUserId = '507f1f77bcf86cd799439011'; // 一个不存在的ObjectId
  
  try {
    const result = await compareProductsByNamesV4Parallel(productNames, invalidUserId);
    
    if (result.success) {
      console.log('✅ 对比成功');
      console.log('📊 返回结果包含userRating字段:', 'userRating' in result.data);
      
      if ('userRating' in result.data) {
        const userRating = result.data.userRating;
        console.log('📝 用户评分信息:', userRating);
        
        if (userRating.hasRated === false && userRating.error) {
          console.log('✅ 正确：无效用户ID，hasRated为false，包含错误信息');
        } else {
          console.log('⚠️ 意外：无效用户ID的处理不正确');
        }
      } else {
        console.log('⚠️ 意外：传入用户ID时应该包含userRating字段');
      }
    } else {
      console.log('❌ 对比失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(userId) {
  try {
    // 删除测试用户
    await User.deleteOne({ _id: userId });
    
    // 删除测试评分
    await ComparisonRating.deleteMany({ userId: userId });
    
    console.log('✅ 测试数据清理完成');
  } catch (error) {
    console.error('❌ 清理测试数据失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('\n🧪 开始用户评分功能测试');
  console.log('='.repeat(80));
  
  await connectDB();
  
  let userId = null;
  let comparisonCacheId = null;
  
  try {
    // 创建测试用户
    userId = await createTestUser();
    
    // 测试场景1: 不传用户ID
    comparisonCacheId = await testWithoutUserId();
    
    // 测试场景2: 传入用户ID但用户未评分
    const cacheId2 = await testWithUserIdNoRating(userId);
    if (!comparisonCacheId) comparisonCacheId = cacheId2;
    
    // 测试场景3: 传入用户ID且用户已评分
    await testWithUserIdHasRating(userId, comparisonCacheId);
    
    // 测试场景4: 传入无效的用户ID
    await testWithInvalidUserId();
    
    console.log('\n🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  } finally {
    // 清理测试数据
    if (userId) {
      await cleanupTestData(userId);
    }
    
    await disconnectDB();
  }
}

main();
