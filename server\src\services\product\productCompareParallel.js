const NewProduct = require('../../models/NewProduct');
const ProductComparisonV4Cache = require('../../models/ProductComparisonV4Cache');
const ComparisonRating = require('../../models/ComparisonRating');
const User = require('../../models/User');
const axios = require('axios');
const { findProductsByNamesV4, formatProductForAI } = require('./productCompare');

/**
 * 产品服务 V4 并行版本 - 基于参数字段提取的智能对比
 * 核心优化：将大任务分解为多个子任务并行处理，显著减少等待时间
 * 子任务划分：technicalSpecs、prosAndCons、usageScenarios、purchaseAdvice
 */

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.4,
  maxTokens: 6000, // 子任务可以使用较少的token
  timeout: 120000  // 子任务超时时间可以适当减少
};

/**
 * 调用DeepSeek API
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 提取产品参数字段和类型信息
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Object} 参数分析结果
 */
function extractProductParametersAndTypes(formattedProducts) {
  console.log('🔍 开始提取产品参数字段和类型信息...');
  
  // 1. 分析产品类型
  const productTypes = [...new Set(formattedProducts.map(p => p.productType))];
  const primaryProductType = productTypes[0];
  const isMixedTypes = productTypes.length > 1;
  
  // 2. 收集所有参数字段
  const allParameters = new Set();
  const parametersByCategory = {};
  const parameterValues = {};
  
  formattedProducts.forEach((product, index) => {
    // 分析 commonSpecs
    if (product.commonSpecs) {
      Object.keys(product.commonSpecs).forEach(category => {
        if (!parametersByCategory[category]) {
          parametersByCategory[category] = new Set();
        }
        
        const categorySpecs = product.commonSpecs[category];
        if (typeof categorySpecs === 'object' && categorySpecs !== null) {
          Object.keys(categorySpecs).forEach(param => {
            allParameters.add(param);
            parametersByCategory[category].add(param);
            
            if (!parameterValues[param]) {
              parameterValues[param] = [];
            }
            parameterValues[param].push({
              productName: product.name,
              value: categorySpecs[param],
              category: category
            });
          });
        }
      });
    }
    
    // 分析 configurations 中的 specs
    if (product.configurations) {
      product.configurations.forEach(config => {
        if (config.specs) {
          Object.keys(config.specs).forEach(category => {
            if (!parametersByCategory[category]) {
              parametersByCategory[category] = new Set();
            }
            
            const categorySpecs = config.specs[category];
            if (typeof categorySpecs === 'object' && categorySpecs !== null) {
              Object.keys(categorySpecs).forEach(param => {
                allParameters.add(param);
                parametersByCategory[category].add(param);
                
                if (!parameterValues[param]) {
                  parameterValues[param] = [];
                }
                parameterValues[param].push({
                  productName: `${product.name} (${config.name})`,
                  value: categorySpecs[param],
                  category: category
                });
              });
            }
          });
        }
      });
    }
  });
  
  // 3. 转换 Set 为 Array 并排序
  const finalParametersByCategory = {};
  Object.keys(parametersByCategory).forEach(category => {
    finalParametersByCategory[category] = Array.from(parametersByCategory[category]).sort();
  });
  
  // 4. 分析参数分类的优先级
  const categoryPriority = Object.keys(finalParametersByCategory)
    .map(category => ({
      category: category,
      parameterCount: finalParametersByCategory[category].length
    }))
    .sort((a, b) => b.parameterCount - a.parameterCount);
  
  // 5. 分析参数覆盖情况
  const parameterCoverage = {};
  Array.from(allParameters).forEach(param => {
    const productCount = new Set(parameterValues[param].map(v => v.productName.split(' (')[0])).size;
    parameterCoverage[param] = {
      totalProducts: formattedProducts.length,
      coveredProducts: productCount,
      coverage: (productCount / formattedProducts.length * 100).toFixed(1) + '%'
    };
  });
  
  const result = {
    productTypes: productTypes,
    primaryProductType: primaryProductType,
    isMixedTypes: isMixedTypes,
    totalParameters: allParameters.size,
    parametersByCategory: finalParametersByCategory,
    parameterValues: parameterValues,
    parameterCoverage: parameterCoverage,
    categoryPriority: categoryPriority,
    extractedCategories: Object.keys(finalParametersByCategory).sort()
  };
  
  console.log(`✅ 参数提取完成: 发现 ${result.totalParameters} 个参数，分布在 ${result.extractedCategories.length} 个类别中`);
  console.log("result: ",result);
  return result;
}

/**
 * 过滤产品数据，只保留指定类别的参数
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {String} category 要保留的参数类别
 * @returns {Array} 过滤后的产品数据
 */
function filterProductsByCategory(formattedProducts, category) {
  return formattedProducts.map(product => {
    const filteredProduct = {
      name: product.name,
      productType: product.productType
    };

    // 只保留指定类别的commonSpecs
    if (product.commonSpecs && product.commonSpecs[category]) {
      filteredProduct.commonSpecs = {
        [category]: product.commonSpecs[category]
      };
    }

    // 只保留指定类别的configurations specs
    if (product.configurations && product.configurations.length > 0) {
      filteredProduct.configurations = product.configurations.map(config => {
        const filteredConfig = {
          name: config.name
        };
        
        if (config.specs && config.specs[category]) {
          filteredConfig.specs = {
            [category]: config.specs[category]
          };
        }
        
        return filteredConfig;
      }).filter(config => config.specs); // 只保留有相关specs的配置
    }

    return filteredProduct;
  });
}

/**
 * 生成技术规格分析子任务（按类别）
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {String} category 要分析的参数类别
 * @param {Array<String>} parameters 该类别下的参数列表
 * @returns {Promise<Object>} 单个类别的技术规格分析结果
 */
async function generateTechnicalSpecsAnalysisByCategory(formattedProducts, category, parameters) {
  // 过滤产品数据，只保留当前类别相关的参数
  const filteredProducts = filterProductsByCategory(formattedProducts, category);
  
  const systemPrompt = `你是一个资深的产品评测专家，专门负责特定技术类别的对比分析。

请根据提供的产品信息，仅针对 **${category}** 类别下的技术参数进行深入对比，并以严格的JSON格式返回。

分析要求：
1. 专注于 **${category}** 类别，不要分析其他类别的参数。
2. 仔细分析提供的每一个技术参数，确保全面覆盖,你可以根据产品实际参数数据，按照逻辑相关性进行对要分析的参数进行合理合并和分类，确保每个重要参数都被包含.
3. analysis字段要详细且专业，包含各产品的具体参数值、参数差异说明、对实际使用体验的影响分析。
4. 客观公正，严格基于提供的产品数据进行分析。

JSON格式要求：
{
  "category": "${category}",
  "items": [
    {
      "name": "你通过合并和分类的技术参数名称",
      "productValues": {"产品A":"产品A在这个技术参数名称上参数的总结","产品B":"产品B在这个技术参数名称上参数的总结"},
      "analysis": "AI对该技术参数的专业分析和总结，站在消费者的角度进行分析，包括参数差异说明、对实际使用体验的影响分析、哪些参数更优以及原因、适用场景等。不要重复产品的具体参数值，专注于分析和比较。"
    }
  ]
}`;

  const userPrompt = `请基于以下产品信息，生成 **${category}** 类别的技术规格对比分析：

产品数据：
${JSON.stringify(filteredProducts, null, 2)}

需要分析的 **${category}** 类别下的参数：
- ${parameters.join('\n- ')}
- 你可以根据产品实际参数数据，按照逻辑相关性进行对要分析的参数进行合理合并和分类，确保每个重要参数都被包含

**重要约束 - 产品名称使用规范**：
在生成JSON响应时，productValues对象中的产品名称键（key）必须严格使用以下名称：
${filteredProducts.map((product, index) => `${index + 1}. "${product.name}"`).join('\n')}

请仔细检查产品数据，确保在分析中全面覆盖上述参数。`;

  console.log("userPrompt: ",userPrompt);

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error(`AI响应中未找到有效的JSON格式 (类别: ${category})`);
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error(`❌ 技术规格分析JSON解析失败 (类别: ${category}):`, parseError.message);
    // 返回一个符合结构的空对象，避免整个流程失败
    return { category: category, items: [] };
  }
}

/**
 * 生成优缺点分析子任务
 */
async function generateProsAndConsAnalysis(formattedProducts) {
  const systemPrompt = `你是一个资深的产品评测专家，专门负责产品优缺点分析。

请根据提供的产品信息，生成每个产品的优缺点分析，并以严格的JSON格式返回。

分析要求：
1. 基于实际技术参数分析优缺点
2. 优缺点要具体且实用
3. 综合评价要基于实际技术参数说明适合什么用户群体
4. 客观公正，严格基于提供的产品数据进行分析

JSON格式要求：
{
  "prosAndCons": [
    {
      "productName": "产品名称",
      "pros": ["基于实际参数的具体优点1", "具体优点2", "具体优点3", "具体优点4", "具体优点5", "具体优点6"],
      "cons": ["基于实际参数的具体缺点1", "具体缺点2", "具体缺点3", "具体缺点4"],
      "overallRating": "详细的综合评价，要基于实际技术参数说明适合什么用户群体"
    }
  ]
}`;

  const userPrompt = `请基于以下产品信息生成优缺点分析：

产品数据：
${JSON.stringify(formattedProducts, null, 2)}

**重要约束 - 产品名称使用规范**：
在prosAndCons数组中的productName字段必须严格使用以下名称：
${formattedProducts.map((product, index) => `${index + 1}. "${product.name}"`).join('\n')}`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('❌ 优缺点分析JSON解析失败:', parseError.message);
    return { prosAndCons: [] };
  }
}

/**
 * 生成使用场景分析子任务
 */
async function generateUsageScenariosAnalysis(formattedProducts) {
  const systemPrompt = `你是一个资深的产品评测专家，专门负责使用场景分析。

请根据提供的产品信息，生成基于产品实际特性的使用场景推荐，并以严格的JSON格式返回。

分析要求：
1. 使用场景要具体到实际应用
2. 基于产品的实际特性进行推荐
3. 推荐理由要详细且基于技术特性
4. 客观公正，严格基于提供的产品数据进行分析

JSON格式要求：
{
  "usageScenarios": [
    {
      "scenario": "基于产品实际特性的具体使用场景名称",
      "description": "详细的场景描述，说明具体的使用需求",
      "recommendedProduct": "推荐产品名称",
      "reason": "详细的推荐理由，要基于产品的实际技术特性说明为什么适合这个场景"
    }
  ]
}`;

  const userPrompt = `请基于以下产品信息生成使用场景分析：

产品数据：
${JSON.stringify(formattedProducts, null, 2)}

**重要约束 - 产品名称使用规范**：
在usageScenarios数组中的recommendedProduct字段必须严格使用以下名称：
${formattedProducts.map((product, index) => `${index + 1}. "${product.name}"`).join('\n')}`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('❌ 使用场景分析JSON解析失败:', parseError.message);
    return { usageScenarios: [] };
  }
}

/**
 * 生成购买建议分析子任务
 */
async function generatePurchaseAdviceAnalysis(formattedProducts) {
  const systemPrompt = `你是一个资深的产品评测专家，专门负责购买建议分析。

请根据提供的产品信息，生成购买建议，并以严格的JSON格式返回。

分析要求：
1. 针对性建议要基于产品的实际特性
2. 重要注意事项要基于产品实际特性
3. 不要添加和价格、购买时机等相关的建议
4. 客观公正，严格基于提供的产品数据进行分析

JSON格式要求：
{
  "purchaseAdvice": {
    "specificAdvice": [
      {
        "userType": "具体用户类型",
        "recommendation": "详细的针对性建议，要基于产品的实际特性"
      }
    ],
    "importantNotes": ["基于产品实际特性的重要注意事项1", "重要注意事项2", "重要注意事项3"]
  }
}`;

  const userPrompt = `请基于以下产品信息生成购买建议：

产品数据：
${JSON.stringify(formattedProducts, null, 2)}`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('❌ 购买建议分析JSON解析失败:', parseError.message);
    return { 
      purchaseAdvice: {
        specificAdvice: [],
        importantNotes: []
      }
    };
  }
}

/**
 * 并行生成结构化的智能对比报告
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {Object} parameterAnalysis 参数分析结果
 * @returns {Promise<Object>} 结构化的对比分析报告
 */
async function generateStructuredComparisonReportParallel(formattedProducts, parameterAnalysis) {
  console.log('🚀 开始超并行AI分析任务...');
  const startTime = Date.now();

  const measureTask = async (taskPromise, taskName) => {
    const taskStartTime = Date.now();
    console.log(`[${taskName}] 子任务开始...`);
    try {
      const result = await taskPromise;
      const taskEndTime = Date.now();
      console.log(`[${taskName}] 子任务完成，耗时: ${taskEndTime - taskStartTime}ms`);
      return result;
    } catch (error) {
      const taskEndTime = Date.now();
      console.error(`[${taskName}] 子任务失败，耗时: ${taskEndTime - taskStartTime}ms. 错误: ${error.message}`);
      throw error;
    }
  };

  try {
    // 动态创建技术规格分析任务
    const techSpecTasks = Object.entries(parameterAnalysis.parametersByCategory).map(([category, params]) => 
      measureTask(
        generateTechnicalSpecsAnalysisByCategory(formattedProducts, category, params),
        `技术规格 - ${category}`
      )
    );

    // 创建其他分析任务
    const otherTasks = [
      measureTask(generateProsAndConsAnalysis(formattedProducts), '优缺点分析'),
      measureTask(generateUsageScenariosAnalysis(formattedProducts), '使用场景分析'),
      measureTask(generatePurchaseAdviceAnalysis(formattedProducts), '购买建议分析')
    ];

    // 合并所有任务并并行执行
    const allTasks = [...techSpecTasks, ...otherTasks];
    const results = await Promise.all(allTasks);

    const endTime = Date.now();
    console.log(`✅ 超并行AI分析完成，总耗时: ${endTime - startTime}ms`);

    // 分离技术规格结果和其他结果
    const technicalSpecsResults = results.slice(0, techSpecTasks.length);
    const otherResults = results.slice(techSpecTasks.length);

    // 合并所有子任务的结果
    const structuredReport = {
      technicalSpecs: technicalSpecsResults.filter(res => res && res.items && res.items.length > 0),
      prosAndCons: otherResults[0].prosAndCons || [],
      usageScenarios: otherResults[1].usageScenarios || [],
      purchaseAdvice: otherResults[2].purchaseAdvice || {
        specificAdvice: [],
        importantNotes: []
      }
    };

    return structuredReport;

  } catch (error) {
    console.error('❌ 超并行AI分析失败:', error.message);
    const endTime = Date.now();
    console.log(`总耗时: ${endTime - startTime}ms`);

    return {
      technicalSpecs: [],
      prosAndCons: [],
      usageScenarios: [],
      purchaseAdvice: {
        specificAdvice: [],
        importantNotes: []
      },
      error: `超并行AI分析失败: ${error.message}`
    };
  }
}

/**
 * 根据产品名称列表获取产品参数对比数据（V4并行版本）
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} userId 可选的用户ID，用于查询用户评分
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesV4Parallel = async (productNames, userId = null) => {
  try {
    console.log('🔍 开始产品对比 V4 并行版本 - 基于参数字段提取的智能分析');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中查找产品
    const findResult = await findProductsByNamesV4(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 检查缓存
    console.log('🔍 检查缓存中是否存在对比结果...');
    const cachedResult = await ProductComparisonV4Cache.findByProductNames(productNames);

    if (cachedResult) {
      console.log('✅ 找到缓存结果');

      // 获取缓存的结果
      const result = cachedResult.getCachedResult();

      // 如果提供了用户ID，查询用户评分
      if (userId) {
        try {
          console.log('🔍 查询用户评分...');

          // 验证用户是否存在
          const user = await User.findById(userId);
          if (!user) {
            console.log('⚠️ 用户不存在，跳过评分查询');
          } else {
            // 查询用户对此对比结果的评分
            const userRating = await ComparisonRating.getUserRating(cachedResult._id, userId);

            if (userRating) {
              console.log('✅ 找到用户评分:', userRating.rating);
              // 将用户评分添加到返回结果中
              result.data.userRating = {
                rating: userRating.rating,
                ratedAt: userRating.createdAt,
                hasRated: true
              };
            } else {
              console.log('📝 用户尚未评分');
              result.data.userRating = {
                rating: null,
                ratedAt: null,
                hasRated: false
              };
            }
          }
        } catch (ratingError) {
          console.error('❌ 查询用户评分失败:', ratingError.message);
          // 评分查询失败不影响主要功能，继续返回缓存结果
          result.data.userRating = {
            rating: null,
            ratedAt: null,
            hasRated: false,
            error: '评分查询失败'
          };
        }
      }

      return result;
    }
    
    console.log('❌ 未找到缓存，开始并行AI分析...');

    // 4. 格式化产品数据用于 AI 分析
    const formattedProductData = products.map(product => formatProductForAI(product));
    console.log('格式化后的产品数据:', formattedProductData)

    // 5. 提取产品参数字段和类型信息
    console.log('🔍 提取产品参数字段和类型信息...');
    const parameterAnalysis = extractProductParametersAndTypes(formattedProductData);
    console.log('提取的产品参数字段和类型信息:', parameterAnalysis)

    // 6. 使用并行AI进行产品对比分析
    console.log('🤖 调用并行AI进行基于参数字段的智能产品对比分析...');
    const structuredReport = await generateStructuredComparisonReportParallel(formattedProductData, parameterAnalysis);

    if (!structuredReport) {
      return {
        success: false,
        error: 'AI 分析失败：未能生成对比报告',
        data: null
      };
    }

    // 7. 分析产品类别信息
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes[0];
    const isSameCategory = true;
    const crossCategoryNote = null;

    // 8. 构建最终返回结果
    const result = {
      success: true,
      data: {
        // 产品基本信息
        products: products.map(product => ({
          skuName: product.skuName,
          imageUrl: product.imageUrl
        })),

        // AI 结构化对比分析结果
        aiAnalysis: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          crossCategoryNote: crossCategoryNote,
          structuredReport: structuredReport,
          aiModel: DEEPSEEK_MODEL,
          processingMode: 'parallel' // 标识使用了并行处理
        },
        
        // 添加默认的评分统计信息
        ratingStats: {
          averageRating: 0,
          totalRatings: 0,
          lastRatingUpdate: null
        },

        userRating : {
          rating: null,
          ratedAt: null,
          hasRated: false
        }
      }
    };
    
    // 9. 保存到缓存
    let comparisonCacheId = null;
    try {
      console.log('💾 保存对比结果到缓存...');
      const savedCache = await ProductComparisonV4Cache.createCache(productNames, result);
      comparisonCacheId = savedCache._id;
      console.log('✅ 缓存保存成功，ID:', comparisonCacheId);
    } catch (cacheError) {
      console.error('⚠️ 缓存保存失败:', cacheError.message);
    }

    // 10. 将缓存ID添加到返回结果中
    if (comparisonCacheId) {
      result.data.comparisonCacheId = comparisonCacheId;
    }

    // 11. 如果提供了用户ID，查询用户评分
    if (userId && comparisonCacheId) {
      try {
        console.log('🔍 查询用户评分...');

        // 验证用户是否存在
        const user = await User.findById(userId);
        if (!user) {
          console.log('⚠️ 用户不存在，跳过评分查询');
          result.data.userRating = {
            rating: null,
            ratedAt: null,
            hasRated: false,
            error: '用户不存在'
          };
        } else {
          // 查询用户对此对比结果的评分
          const userRating = await ComparisonRating.getUserRating(comparisonCacheId, userId);

          if (userRating) {
            console.log('✅ 找到用户评分:', userRating.rating);
            result.data.userRating = {
              rating: userRating.rating,
              ratedAt: userRating.createdAt,
              hasRated: true
            };
          } else {
            console.log('📝 用户尚未评分');
            result.data.userRating = {
              rating: null,
              ratedAt: null,
              hasRated: false
            };
          }
        }
      } catch (ratingError) {
        console.error('❌ 查询用户评分失败:', ratingError.message);
        // 评分查询失败不影响主要功能
        result.data.userRating = {
          rating: null,
          ratedAt: null,
          hasRated: false,
          error: '评分查询失败'
        };
      }
    }

    console.log('✅ 产品对比 V4 并行版本完成');
    return result;

  } catch (error) {
    console.error('❌ 产品对比 V4 并行版本失败:', error);
    return {
      success: false,
      error: `产品对比失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  compareProductsByNamesV4Parallel,
  // generateStructuredComparisonReportParallel,
  // generateProsAndConsAnalysis,
  // generateUsageScenariosAnalysis,
  // generatePurchaseAdviceAnalysis
};