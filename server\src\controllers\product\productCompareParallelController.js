const { compareProductsByNamesV4Parallel } = require('../../services/product/productCompareParallel');
const { success, error } = require('../../utils/response');
const { compareProductsV4ParallelSchema } = require('../../validators/productvalidator');

/**
 * @desc    产品参数对比 V4并行版本 - 基于参数字段提取的智能对比（并行处理）
 * @route   POST /api/v1/products/compare-v4-parallel
 * @access  Public
 */
const compareProductsV4Parallel = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = compareProductsV4ParallelSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productNames } = value;

    console.log('🚀 开始并行产品对比 V4，产品列表:', productNames);

    // 调用服务层进行并行产品对比
    const result = await compareProductsByNamesV4Parallel(productNames);

    if (!result.success) {
      console.error('❌ 并行产品对比失败:', result.error);
      return error(res, 400, result.error, result.data);
    }

    console.log('✅ 并行产品对比成功完成');

    // 返回成功响应
    const responseData = {
      ...result.data,
      meta: {
        requestedProducts: productNames,
        actualProductCount: result.data.products.length,
        processingMode: 'parallel',
        aiModel: result.data.aiAnalysis?.aiModel || 'deepseek-chat',
        description: '基于参数字段提取的智能产品对比分析（并行处理版本）'
      }
    };

    return success(res, 200, '并行产品对比分析完成', responseData);

  } catch (err) {
    console.error('并行产品对比控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('至少需要提供')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('最多支持')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('DeepSeek')) {
      return error(res, 503, 'AI分析服务暂时不可用，请稍后重试');
    }

    return error(res, 500, '并行产品对比服务暂时不可用，请稍后重试');
  }
};

module.exports = {
  compareProductsV4Parallel
};