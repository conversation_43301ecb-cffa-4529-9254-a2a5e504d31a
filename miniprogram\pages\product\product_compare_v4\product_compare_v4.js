// pages/product/product_compare_v4/product_compare_v4.js
const api = require('../../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 对比结果数据
    compareResult: null,
    
    // 页面状态
    loading: false,
    error: null,
    
    // 产品名称列表
    productNames: [],
    
    // 展示控制
    expandedCategories: {}, // 控制各个分类的展开状态
    showFullReport: false, // 是否显示完整报告
    
    // 当前查看的产品索引（用于移动端切换查看）
    currentProductIndex: 0,
    
    // 使用场景展开状态
    expandedScenarios: {},
    
    // 购买建议展开状态
    expandedPurchaseAdvice: {},
    
    // 优缺点分析展开状态
    expandedProsAndCons: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('产品对比V4页面加载参数:', options);

    if (options.productNames) {
      try {
        console.log('原始productNames参数:', options.productNames);
        const decodedNames = decodeURIComponent(options.productNames);
        console.log('解码后的productNames:', decodedNames);

        const productNames = JSON.parse(decodedNames);
        console.log('解析后的productNames:', productNames);

        if (Array.isArray(productNames) && productNames.length >= 2) {
          console.log('产品名称验证通过，设置数据...');
          this.setData({
            productNames: productNames
          }, () => {
            console.log('产品名称设置完成，开始加载对比结果...');
            this.loadCompareResult();
          });
        } else {
          console.error('产品名称格式错误:', productNames);
          this.setData({
            error: '产品名称参数格式错误'
          });
        }
      } catch (e) {
        console.error('解析产品名称参数失败:', e);
        console.error('原始参数:', options.productNames);
        this.setData({
          error: '产品名称参数解析失败: ' + e.message
        });
      }
    } else {
      console.error('缺少产品名称参数');
      this.setData({
        error: '缺少产品名称参数'
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时可以做一些操作
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadCompareResult();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { productNames } = this.data;
    
    if (productNames && productNames.length > 0) {
      return {
        title: `${productNames.join(' vs ')} - 产品对比`,
        path: `/pages/product/product_compare_v4/product_compare_v4?productNames=${encodeURIComponent(JSON.stringify(productNames))}`,
        imageUrl: ''
      };
    }
    
    return {
      title: '产品对比',
      path: '/pages/product/product_compare_v4/product_compare_v4'
    };
  },

  /**
   * 加载对比结果
   */
  loadCompareResult() {
    console.log('开始加载对比结果，产品名称:', this.data.productNames);

    if (!this.data.productNames || this.data.productNames.length < 2) {
      console.error('产品名称列表无效:', this.data.productNames);
      this.setData({
        error: '产品名称列表为空或数量不足'
      });
      return;
    }

    this.setData({
      loading: true,
      error: null
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '产品对比'
    });

    console.log('准备调用API，产品名称:', this.data.productNames);

    // 添加请求开始时间
    const startTime = Date.now();

    // api.product.compareProductsV4(this.data.productNames)
    api.product.compareProductsV4Parallel(this.data.productNames)
      .then(res => {
        const endTime = Date.now();
        console.log(`API调用完成，耗时: ${endTime - startTime}ms`);
        console.log('获取产品对比结果成功:', res);

        if (res.success) {
          console.log('对比结果数据:', res.data);
          console.log('评分统计:', res.data.ratingStats);
          console.log('缓存ID:', res.data.comparisonCacheId);

          // 检查数据完整性
          if (!res.data.aiAnalysis) {
            console.warn('警告: 缺少aiAnalysis数据');
          }
          if (!res.data.products || res.data.products.length === 0) {
            console.warn('警告: 缺少products数据');
          }

          console.log('准备设置页面数据...');
          this.setData({
            compareResult: res.data,
            loading: false
          }, () => {
            console.log('页面数据设置完成');
          });

          // 初始化展开状态
          this.initExpandedStates();

        } else {
          console.error('API返回失败:', res.message);
          this.setData({
            error: res.message || '获取产品对比结果失败',
            loading: false
          });
        }
      })
      .catch(err => {
        const endTime = Date.now();
        console.error(`API调用失败，耗时: ${endTime - startTime}ms`);
        console.error('获取产品对比结果失败:', err);
        this.setData({
          error: err.message || '网络错误，请重试',
          loading: false
        });
      })
      .finally(() => {
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 初始化展开状态
   */
  initExpandedStates() {
    console.log('初始化展开状态...');
    const { compareResult } = this.data;
    if (!compareResult || !compareResult.aiAnalysis || !compareResult.aiAnalysis.structuredReport) {
      console.log('缺少必要的数据结构，跳过展开状态初始化');
      return;
    }

    // 初始化为空对象，所有分类和场景默认都不展开
    const expandedCategories = {};
    const expandedScenarios = {};
    const expandedProsAndCons = {};

    // 注释掉自动展开逻辑，改为默认全部收起状态
    // const technicalSpecs = compareResult.aiAnalysis.structuredReport.technicalSpecs || [];
    // technicalSpecs.slice(0, 3).forEach((spec, index) => {
    //   expandedCategories[spec.category] = true;
    // });

    // const usageScenarios = compareResult.aiAnalysis.structuredReport.usageScenarios || [];
    // usageScenarios.slice(0, 2).forEach((scenario, index) => {
    //   expandedScenarios[scenario.scenario] = true;
    // });

    // 初始化购买建议展开状态
    const expandedPurchaseAdvice = {};

    console.log('设置展开状态数据...');
    this.setData({
      expandedCategories,
      expandedScenarios,
      expandedProsAndCons,
      expandedPurchaseAdvice
    }, () => {
      console.log('展开状态设置完成');
    });
  },

  /**
   * 测试用：加载本地测试数据
   */
  loadTestData() {
    console.log('加载测试数据...');

    // 模拟测试数据
    const testData = {
      products: [
        {
          skuName: "华为 Mate 70 Pro",
          imageUrl: "https://example.com/phone-image.jpg",
          _id: "688620eba1d79b75854040fe"
        },
        {
          skuName: "苹果iPhone 16 Pro",
          imageUrl: "https://example.com/phone-image.jpg",
          _id: "688620eba1d79b75854040ff"
        }
      ],
      ratingStats: {
        averageRating: 4.2,
        totalRatings: 15,
        lastRatingUpdate: null
      },
      comparisonCacheId: "688620eba1d79b75854040fd",
      aiAnalysis: {
        structuredReport: {
          summary: {
            title: "华为 Mate 70 Pro vs 苹果iPhone 16 Pro 对比分析",
            productCount: 2,
            category: "手机"
          }
        }
      }
    };

    this.setData({
      compareResult: testData,
      loading: false,
      error: null
    }, () => {
      console.log('测试数据设置完成');
      this.initExpandedStates();
    });
  },

  /**
   * 切换技术规格分类展开状态
   */
  toggleCategory(e) {
    const category = e.currentTarget.dataset.category;
    const { expandedCategories } = this.data;
    
    this.setData({
      [`expandedCategories.${category}`]: !expandedCategories[category]
    });
  },

  /**
   * 切换使用场景展开状态
   */
  toggleScenario(e) {
    const scenario = e.currentTarget.dataset.scenario;
    const { expandedScenarios } = this.data;
    
    this.setData({
      [`expandedScenarios.${scenario}`]: !expandedScenarios[scenario]
    });
  },

  /**
   * 切换完整报告显示状态
   */
  toggleFullReport() {
    this.setData({
      showFullReport: !this.data.showFullReport
    });
  },

  /**
   * 切换购买建议子模块展开状态
   */
  togglePurchaseAdviceSection(e) {
    const section = e.currentTarget.dataset.section;
    const { expandedPurchaseAdvice } = this.data;
    
    this.setData({
      [`expandedPurchaseAdvice.${section}`]: !expandedPurchaseAdvice[section]
    });
  },

  /**
   * 切换优缺点分析展开状态
   */
  toggleProsAndCons(e) {
    const productName = e.currentTarget.dataset.productName;
    const { expandedProsAndCons } = this.data;
    
    this.setData({
      [`expandedProsAndCons.${productName}`]: !expandedProsAndCons[productName]
    });
  },

  /**
   * 切换当前查看的产品（移动端）
   */
  switchProduct(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentProductIndex: index
    });
  },

  /**
   * 复制对比结果
   */
  copyCompareResult() {
    const { compareResult } = this.data;
    if (!compareResult || !compareResult.aiAnalysis) return;

    const summary = compareResult.aiAnalysis.structuredReport.summary;
    const copyText = `${summary.title}\n\n关键差异：\n${summary.keyDifferences.join('\n')}`;

    wx.setClipboardData({
      data: copyText,
      success: () => {
        wx.showToast({
          title: '对比结果已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 重新加载
   */
  onRetry() {
    this.loadCompareResult();
  },

  /**
   * 查看产品详情
   */
  viewProductDetail(e) {
    const productName = e.currentTarget.dataset.productName;
    if (!productName) return;

    wx.navigateTo({
      url: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(productName)}`
    });
  },

  /**
   * 预览产品图片
   */
  previewProductImage(e) {
    const imageUrl = e.currentTarget.dataset.imageUrl;
    if (!imageUrl) return;

    wx.previewImage({
      urls: [imageUrl],
      current: imageUrl
    });
  },

  /**
   * 分享对比结果
   */
  shareCompareResult() {
    const { productNames } = this.data;
    if (!productNames || productNames.length === 0) return;

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 处理评分提交事件
   */
  async onSubmitRating(e) {
    const { targetId, ratingType, rating, onSuccess, onError } = e.detail;
    console.log('处理评分提交:', { targetId, ratingType, rating });

    try {
      // 根据评分类型调用不同的API
      let result;
      if (ratingType === 'comparison') {
        result = await api.user.rateComparison(targetId, rating);
      } else {
        throw new Error(`不支持的评分类型: ${ratingType}`);
      }

      if (result.success) {
        // 调用组件的成功回调
        onSuccess(result.data);
      } else {
        throw new Error(result.message || '评分失败');
      }
    } catch (error) {
      console.error('评分提交失败:', error);
      // 调用组件的错误回调
      onError(error);
    }
  },

  /**
   * 处理评分更新事件
   */
  onRatingUpdated(e) {
    const { averageRating, totalRatings, userRating } = e.detail;
    console.log('评分更新:', { averageRating, totalRatings, userRating });

    // 更新页面数据中的评分统计
    this.setData({
      'compareResult.ratingStats.averageRating': averageRating,
      'compareResult.ratingStats.totalRatings': totalRatings
    });

    // 显示评分成功提示
    wx.showToast({
      title: `评分成功: ${userRating}分`,
      icon: 'success',
      duration: 2000
    });
  }
});